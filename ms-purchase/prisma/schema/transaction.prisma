model Transaction {
  id            String   @id @default(uuid()) @map("transaction_id")
  register      Register @relation(fields: [registerId], references: [id])
  registerId    String   @map("register_id")
  orderId       String?  @map("order_id")
  amount        Float
  type          String   @db.VarChar(20)
  paymentMethod String   @db.VarChar(20)
  processedAt   DateTime @default(now()) @map("processed_at")
  status        String   @db.VarChar(20) @default("PENDING")
  metadata      Json?

  @@index([orderId], name: "order_transaction_index")
}