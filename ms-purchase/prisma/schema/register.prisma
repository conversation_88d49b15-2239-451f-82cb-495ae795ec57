model Register {
  id             String   @id @default(uuid()) @map("register_id")
  storeId        String   @map("store_id")
  cashierId      String   @map("cashier_id")
  status         String   @db.VarChar(20) @default("CLOSED")
  openedAt       DateTime @default(now()) @map("opened_at")
  closedAt       DateTime? @map("closed_at")
  openingBalance Float    @map("opening_balance")
  closingBalance Float?   @map("closing_balance")
  transactions   Transaction[]
  metadata       Json?

  @@index([storeId], name: "store_register_index")
}

