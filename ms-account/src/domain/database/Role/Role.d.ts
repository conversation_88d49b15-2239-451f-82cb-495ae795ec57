import type { Acl as AclModel, Role as RoleModel } from '@prisma/client'

import type { PaginationParams } from '@thrift/common/engines/Pagination/Pagination'

export type FetchRolesProps = PaginationParams

export type FetchRoleDto = {
  id: string
}

export type CreateRoleDto = {
  shortname: string
  name: string
  description: string
}

export type AssignAclsToRoleDto = {
  roleId: string
  aclIds: string[]
}

export type UpdateRoleDto = FetchRoleDto & CreateRoleDto

export type RoleResponse =
  | (Pick<RoleModel, 'id' | 'shortname' | 'description' | 'name'> & {
      acls: Pick<AclModel, 'id' | 'tagname' | 'description' | 'name'>[]
    })
  | null
