import type { PaginationParams } from '@thrift/common/engines/Pagination/Pagination'

export type FetchRolesProps = PaginationParams

export type FetchRoleProps = { id: string }

export type CreateRoleDto = {
  shortname: string
  name: string
  description: string
}

export type UpdateRoleDto = CreateRoleDto & {
  id: string
}

export type AssignAclsToRoleDto = {
  roleId: string
  aclIds: string[]
}
