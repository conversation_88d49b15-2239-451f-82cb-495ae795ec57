# MS-Evaluation

Microservice responsible for managing the evaluation process of used products submitted by suppliers for subsequent purchase. Acts as a bridge between item submission and ms-stock, passing through ms-purchase.

## Architecture

This microservice follows Clean Architecture principles with three main layers:

- **Domain Layer**: Database operations using Prisma, error handling with custom exceptions
- **Application Layer**: Business logic, validation using Zod schemas, CRUD operations
- **Resources Layer**: REST API endpoints, request/response handling

## Technologies

- TypeScript/Node.js v20-22
- Prisma ORM with MySQL 8
- Express.js for REST API
- Zod for validation
- Docker + Docker Compose
- ESLint + Prettier

## Database Schema

### EvaluationBatch
- Evaluation batch submitted by a supplier
- Contains supplier info, store info, status, display code, and notes

### EvaluationItem
- Individual item within a batch
- Contains product details, evaluation results, pricing information

### EvaluationProposal
- Formal proposal sent to supplier
- Contains total value, status, and supplier response

## API Endpoints

### Evaluation Batches
- `POST /evaluation-batches` - Create batch
- `GET /evaluation-batches` - List with filters and pagination
- `GET /evaluation-batches/{id}` - Get batch with items
- `PUT /evaluation-batches/{id}` - Update status/notes
- `DELETE /evaluation-batches/{id}` - Soft delete batch

### Evaluation Items
- `POST /evaluation-batches/{batchId}/items` - Add item
- `GET /evaluation-items` - List with filters
- `GET /evaluation-items/{id}` - Get item details
- `PUT /evaluation-items/{id}` - Evaluate item
- `DELETE /evaluation-items/{id}` - Remove item

### Evaluation Proposals
- `POST /evaluation-proposals` - Create proposal
- `GET /evaluation-proposals` - List with filters
- `GET /evaluation-proposals/{id}` - Get proposal details
- `PUT /evaluation-proposals/{id}/send` - Send proposal
- `PUT /evaluation-proposals/{id}/respond` - Supplier response

## Configuration

- **Port**: 8085
- **Database**: MySQL on port 3311
- **Database Name**: evaluation

## Development

```bash
# Install dependencies
yarn install

# Start development server
yarn dev

# Build for production
yarn build

# Run linting
yarn lint

# Format code
yarn format
```

## Docker

```bash
# Start database
docker-compose up database

# Run provisioning (migrations + seeds)
docker-compose up provisioning

# Development with hot reload
docker-compose up dev
```
