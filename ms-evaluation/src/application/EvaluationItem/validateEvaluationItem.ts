import { validateWithZod } from '@thrift/common/engines/Validation/validateWithZod'

import type { CreateEvaluationItemDto } from '@app/application/EvaluationItem'
import { CreateEvaluationItemSchema } from '@app/application/EvaluationItem/EvaluationItem.schema'

export const validateCreateEvaluationItem = (input: unknown): CreateEvaluationItemDto => {
  return validateWithZod(CreateEvaluationItemSchema, input)
}
