import { validate<PERSON><PERSON><PERSON><PERSON> } from '@thrift/common/engines/Validation/validateWithZod'

import type { CreateEvaluationProposalDto } from '@app/application/EvaluationProposal'
import { CreateEvaluationProposalSchema } from '@app/application/EvaluationProposal/EvaluationProposal.schema'

import { EvaluationBatch } from '@app/domain/database/EvaluationBatch'
import { EvaluationProposal } from '@app/domain/database/EvaluationProposal'

export class Create {
  public async create(input: unknown) {
    const dto: CreateEvaluationProposalDto = validateWithZod(
      CreateEvaluationProposalSchema,
      input,
    )

    const evaluationBatchModel = new EvaluationBatch()

    await evaluationBatchModel.findById(dto.evaluationBatchId)

    const model = new EvaluationProposal()

    return model.create(dto)
  }
}
