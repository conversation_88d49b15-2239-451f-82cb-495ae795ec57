import { validate<PERSON><PERSON><PERSON><PERSON> } from '@thrift/common/engines/Validation/validateWithZod'

import {
  type FilterEvaluationProposalDto,
  FilterEvaluationProposalSchema,
} from '@app/application/EvaluationProposal'

import { EvaluationProposal } from '@app/domain/database/EvaluationProposal'

export class Read {
  public async findById(id: string) {
    if (!id) {
      throw new ReferenceError('Evaluation Proposal ID is required')
    }

    const model = new EvaluationProposal()

    return model.findById(id)
  }

  public async find(input: unknown) {
    const filter: FilterEvaluationProposalDto = validateWithZod(
      FilterEvaluationProposalSchema,
      input,
    )

    const model = new EvaluationProposal()

    return model.find(filter)
  }
}
