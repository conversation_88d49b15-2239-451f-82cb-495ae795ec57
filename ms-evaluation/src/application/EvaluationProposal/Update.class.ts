import { validate<PERSON><PERSON><PERSON><PERSON> } from '@thrift/common/engines/Validation/validateWithZod'

import type { UpdateEvaluationProposalDto } from '@app/application/EvaluationProposal'
import { UpdateEvaluationProposalSchema } from '@app/application/EvaluationProposal/EvaluationProposal.schema'

import { EvaluationProposal } from '@app/domain/database/EvaluationProposal'

export class Update {
  public async update(input: unknown) {
    const dto: UpdateEvaluationProposalDto = validateWithZod(
      UpdateEvaluationProposalSchema,
      input,
    )

    const model = new EvaluationProposal()

    return model.update(dto)
  }
}
