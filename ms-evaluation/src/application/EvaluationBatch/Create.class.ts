import { validate<PERSON><PERSON><PERSON><PERSON> } from '@thrift/common/engines/Validation/validateWithZod'

import type { CreateEvaluationBatchDto } from '@app/application/EvaluationBatch'
import { CreateEvaluationBatchSchema } from '@app/application/EvaluationBatch/EvaluationBatch.schema'

import { EvaluationBatch } from '@app/domain/database/EvaluationBatch'

export class Create {
  public async create(input: unknown) {
    const dto: CreateEvaluationBatchDto = validateWithZod(
      CreateEvaluationBatchSchema,
      input,
    )

    const model = new EvaluationBatch()

    // Create the evaluation batch
    return model.create(dto)
  }
}
