import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination'

export const EvaluationBatchBaseSchema = z.object({
  supplierId: z.string().uuid(),
  storeId: z.string().uuid(),
  status: z.enum([
    'PENDING_REVIEW',
    'UNDER_EVALUATION',
    'EVALUATION_COMPLETED',
    'PROPOSAL_SENT',
    'PROPOSAL_ACCEPTED',
    'PROPOSAL_REJECTED',
    'CLOSED',
  ]),
  displayCode: z.string().min(1).max(20),
  notes: z.string().optional(),
})

export const CreateEvaluationBatchSchema = EvaluationBatchBaseSchema

export const UpdateEvaluationBatchSchema = EvaluationBatchBaseSchema.extend({
  id: z.string().uuid(),
}).partial({
  supplierId: true,
  storeId: true,
  status: true,
  displayCode: true,
  notes: true,
})

export const FilterEvaluationBatchSchema = createFilterSchema({
  supplierId: z.string().uuid().optional(),
  storeId: z.string().uuid().optional(),
  status: z
    .enum([
      'PENDING_REVIEW',
      'UNDER_EVALUATION',
      'EVALUATION_COMPLETED',
      'PROPOSAL_SENT',
      'PROPOSAL_ACCEPTED',
      'PROPOSAL_REJECTED',
      'CLOSED',
    ])
    .optional(),
  displayCode: z.string().optional(),
})
