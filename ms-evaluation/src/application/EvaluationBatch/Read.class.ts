import { validate<PERSON><PERSON><PERSON><PERSON> } from '@thrift/common/engines/Validation/validateWithZod'

import {
  type FilterEvaluationBatchDto,
  FilterEvaluationBatchSchema,
} from '@app/application/EvaluationBatch'

import { EvaluationBatch } from '@app/domain/database/EvaluationBatch'

export class Read {
  public async findById(id: string) {
    if (!id) {
      throw new ReferenceError('Evaluation Batch ID is required')
    }

    const model = new EvaluationBatch()

    return model.findById(id)
  }

  public async find(input: unknown) {
    const filter: FilterEvaluationBatchDto = validateWithZod(
      FilterEvaluationBatchSchema,
      input,
    )

    const model = new EvaluationBatch()

    return model.find(filter)
  }
}
