import type { Decimal } from '@prisma/client/runtime/library'

import type { PaginationParams } from '@thrift/common/engines/Pagination/Pagination'

export type EvaluationItemModel = {
  id: string
  evaluationBatchId: string
  supplierDescription?: string | null
  supplierSuggestedCategoryId?: string | null
  evaluatedProductCategoryId?: string | null
  evaluatedProductClassificationId?: string | null
  evaluatorNotes?: string | null
  proposedBuyPrice: Decimal
  status: string
  rejectionReason?: string | null
  estimatedMarketValue?: Decimal | null
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type CreateEvaluationItemDto = {
  evaluationBatchId: string
  supplierDescription?: string
  supplierSuggestedCategoryId?: string
  evaluatedProductCategoryId?: string
  evaluatedProductClassificationId?: string
  evaluatorNotes?: string
  proposedBuyPrice: Decimal
  status: string
  rejectionReason?: string
  estimatedMarketValue?: Decimal
}

export type UpdateEvaluationItemDto = Partial<
  Omit<CreateEvaluationItemDto, 'evaluationBatchId'>
> & {
  id: string
}

export type FetchEvaluationItemsProps = PaginationParams & {
  evaluationBatchId?: string
  status?: string
  evaluatedProductCategoryId?: string
  evaluatedProductClassificationId?: string
}
