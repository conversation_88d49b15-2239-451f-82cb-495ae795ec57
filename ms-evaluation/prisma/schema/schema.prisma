// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider        = "prisma-client-js"
  previewFeatures = []
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model EvaluationBatch {
  id          String   @id @default(uuid())
  supplierId  String   // FK to Person.id from ms-person
  storeId     String   // FK to Store.id from TBD
  status      String   // PENDING_REVIEW, UNDER_EVALUATION, EVALUATION_COMPLETED, PROPOSAL_SENT, PROPOSAL_ACCEPTED, PROPOSAL_REJECTED, CLOSED
  displayCode String   // Code for waiting screen like <PERSON>'s
  notes       String?  // Optional notes
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  deleted     <PERSON><PERSON>an  @default(false)

  items     EvaluationItem[]
  proposals EvaluationProposal[]

  @@map("evaluation_batches")
}

model EvaluationItem {
  id                                String          @id @default(uuid())
  evaluationBatch                   EvaluationBatch @relation(fields: [evaluationBatchId], references: [id], onDelete: Cascade)
  evaluationBatchId                 String
  supplierDescription               String?         // Optional supplier's description
  supplierSuggestedCategoryId       String?         // Optional supplier suggested category
  evaluatedProductCategoryId        String?         // FK to ProductCategory from ms-stock
  evaluatedProductClassificationId  String?         // FK to ProductClassification from ms-stock
  evaluatorNotes                    String?         // Optional evaluator notes
  proposedBuyPrice                  Decimal         @default(0)
  status                            String          // PENDING_EVALUATION, ACCEPTED, REJECTED
  rejectionReason                   String?         // Optional rejection reason
  estimatedMarketValue              Decimal?        // Optional estimated market value
  createdAt                         DateTime        @default(now())
  updatedAt                         DateTime        @updatedAt
  deleted                           Boolean         @default(false)

  @@map("evaluation_items")
}

model EvaluationProposal {
  id                    String          @id @default(uuid())
  evaluationBatch       EvaluationBatch @relation(fields: [evaluationBatchId], references: [id], onDelete: Cascade)
  evaluationBatchId     String
  totalProposedValue    Decimal         @default(0)
  proposalDate          DateTime        @default(now())
  status                String          // DRAFT, SENT_TO_SUPPLIER, ACCEPTED_BY_SUPPLIER, REJECTED_BY_SUPPLIER
  supplierResponseDate  DateTime?       // Optional supplier response date
  supplierNotes         String?         // Optional supplier notes
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt
  deleted               Boolean         @default(false)

  @@map("evaluation_proposals")
}
