import { useLanguage } from "../Language";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoginSchema, LoginSchemaType } from "./Login.schemas";
import { useLoginMutation } from "@app/domain/Login";

export const useLogin = () => {
  const { translate } = useLanguage();
  const { register, handleSubmit } = useForm<LoginSchemaType>({
    resolver: zodResolver(LoginSchema),
  });
  const { postLoginAsync } = useLoginMutation();

  const onSubmit = async (data: LoginSchemaType) => {
    try {
      await postLoginAsync(data);
    } catch (error) {}
  };

  return {
    handleSubmit: handleSubmit(onSubmit),
    register,
    translate,
  };
};
