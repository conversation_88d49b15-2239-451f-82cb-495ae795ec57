import { LoginWrap } from "@thrift/design-system/packages/templates/Login";
import { SignIn } from "@thrift/design-system/packages/organisms/SignIn";
import { SignInBrandInfo } from "@thrift/design-system/packages/organisms/SignInBrandInfo";
import { useLogin } from "@app/application/Login";

const Login = () => {
  const { translate, handleSubmit, register } = useLogin();

  return (
    <LoginWrap>
      <SignInBrandInfo />
      <SignIn
        title="Thrift"
        description={translate("login:description")}
        userNameProps={{
          ...register("username"),
          label: translate("login:username"),
        }}
        passwordProps={{
          label: translate("login:password"),
          ...register("password"),
        }}
        buttonProps={{
          label: translate("login:button"),
          onClick: handleSubmit,
          type: "button",
          disabled: false,
        }}
      />
    </LoginWrap>
  );
};

export default Login;
