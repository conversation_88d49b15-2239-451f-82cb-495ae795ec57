import { validate<PERSON><PERSON><PERSON><PERSON> } from '@thrift/common/engines/Validation/validateWithZod'

import type { CreateDomainDto } from '@app/application/Domain'
import { CreateDomainSchema } from '@app/application/Domain/Domain.schema'

import { Domain } from '@app/domain/database/Domain'

export class Create {
  public async create(input: unknown) {
    const dto: CreateDomainDto = validateWithZod(CreateDomainSchema, input)

    const model = new Domain()

    // Create the domain
    return model.create(dto)
  }
}
