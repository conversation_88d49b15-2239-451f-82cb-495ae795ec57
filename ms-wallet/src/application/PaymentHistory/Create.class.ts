import type { CreatePaymentHistoryDto } from '@app/application/PaymentHistory'
import { validateCreatePaymentHistory } from '@app/application/PaymentHistory/validatePaymentHistory'

import { PaymentHistory } from '@app/domain/database/PaymentHistory'
import { PaymentMethod } from '@app/domain/database/PaymentMethod'

export class Create {
  public async create(input: unknown) {
    // Validate and convert input to proper DTO with Decimal
    const dto: CreatePaymentHistoryDto = validateCreatePaymentHistory(input)

    // Validate that the payment method exists
    const paymentMethodModel = new PaymentMethod()

    await paymentMethodModel.findById(dto.paymentMethodId)

    const model = new PaymentHistory()

    // Create the payment history
    return model.create(dto)
  }
}
