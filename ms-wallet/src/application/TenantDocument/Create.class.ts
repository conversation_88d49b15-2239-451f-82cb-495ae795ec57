import { validate<PERSON><PERSON><PERSON><PERSON> } from '@thrift/common/engines/Validation/validateWithZod'

import type { CreateTenantDocumentDto } from '@app/application/TenantDocument'
import { CreateTenantDocumentSchema } from '@app/application/TenantDocument/TenantDocument.schema'

import { Tenant } from '@app/domain/database/Tenant'
import { TenantDocument } from '@app/domain/database/TenantDocument'

export class Create {
  public async create(input: unknown) {
    const dto: CreateTenantDocumentDto = validateWithZod(
      CreateTenantDocumentSchema,
      input,
    )

    const tenantModel = new Tenant()

    await tenantModel.findById(dto.tenantId)

    const model = new TenantDocument()

    return model.create(dto)
  }
}
