import { validate<PERSON>ith<PERSON>od } from '@thrift/common/engines/Validation/validateWithZod'

import type { CreatePaymentMethodDto } from '@app/application/PaymentMethod'
import { CreatePaymentMethodSchema } from '@app/application/PaymentMethod/PaymentMethod.schema'

import { PaymentMethod } from '@app/domain/database/PaymentMethod'

export class Create {
  public async create(input: unknown) {
    const dto: CreatePaymentMethodDto = validateWithZod(
      CreatePaymentMethodSchema,
      input,
    )

    const model = new PaymentMethod()

    // Create the payment method
    return model.create(dto)
  }
}
