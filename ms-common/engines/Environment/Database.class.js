"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Database = void 0;
const dotenv_1 = require("dotenv");
class Database {
    static setDatabaseUrl({ driver, username, password, host, port, schema, }) {
        (0, dotenv_1.populate)(process.env, {
            DATABASE_URL: `${driver}://${username}:${password}@${host}:${port}/${schema}`,
        });
    }
}
exports.Database = Database;
//# sourceMappingURL=Database.class.js.map